import React, { useEffect, useState, useRef, useCallback } from "react";

// Utility function to convert AudioBuffer to WAV format
function audioBufferToWav(buffer) {
  const numberOfChannels = buffer.numberOfChannels;
  const sampleRate = buffer.sampleRate;
  const format = 1; // PCM
  const bitDepth = 16;

  let result = new Float32Array(buffer.length * numberOfChannels);
  for (let channel = 0; channel < numberOfChannels; channel++) {
    const channelData = buffer.getChannelData(channel);
    for (let i = 0; i < channelData.length; i++) {
      result[i * numberOfChannels + channel] = channelData[i];
    }
  }

  // Convert to 16-bit PCM
  const bytesPerSample = bitDepth / 8;
  const blockAlign = numberOfChannels * bytesPerSample;
  const dataSize = result.length * bytesPerSample;
  const buffer16Bit = new Int16Array(result.length);
  for (let i = 0; i < result.length; i++) {
    const sample = Math.max(-1, Math.min(1, result[i]));
    buffer16Bit[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff;
  }

  // Create WAV header
  const headerLength = 44;
  const wavData = new ArrayBuffer(headerLength + dataSize);
  const view = new DataView(wavData);

  // RIFF chunk descriptor
  writeString(view, 0, "RIFF");
  view.setUint32(4, 36 + dataSize, true);
  writeString(view, 8, "WAVE");

  // fmt sub-chunk
  writeString(view, 12, "fmt ");
  view.setUint32(16, 16, true); // fmt chunk size
  view.setUint16(20, format, true);
  view.setUint16(22, numberOfChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, sampleRate * blockAlign, true);
  view.setUint16(32, blockAlign, true);
  view.setUint16(34, bitDepth, true);

  // data sub-chunk
  writeString(view, 36, "data");
  view.setUint32(40, dataSize, true);

  // Write audio data
  const offset = 44;
  const buffer8 = new Uint8Array(wavData, offset);
  const buffer8Bit = new Uint8Array(buffer16Bit.buffer);
  buffer8.set(buffer8Bit);

  return new Uint8Array(wavData);
}

function writeString(view, offset, string) {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
}
import toast from "react-hot-toast";
import VideoPreview from "../smaller_comp/VideoPreview";
import QuestionDisplay from "../smaller_comp/QuestionDisplay";
import AnswerInput from "../smaller_comp/AnswerInput";
import { Mic, Video } from "lucide-react";
import { PollyClient, SynthesizeSpeechCommand } from "@aws-sdk/client-polly";
import { fromCognitoIdentityPool } from "@aws-sdk/credential-provider-cognito-identity";

// Configure Polly Client using Cognito Identity Pool credentials
const pollyClient = new PollyClient({
  region: import.meta.env.VITE_AWS_REGION,
  credentials: fromCognitoIdentityPool({
    clientConfig: { region: import.meta.env.VITE_AWS_REGION },
    identityPoolId: import.meta.env.VITE_AWS_POOL_ID,
  }),
});

export default function InterviewSection({
  localCamStream,
  questions,
  currentQuestion,
  currentIndex,
  onNextQuestion,
  onEndInterview,
  error,
  isLoading,
  startAnswering,
  updateAnswer, // Add updateAnswer prop
  isInterviewActive = false, // New prop to control when interview is active
}) {
  // State

  const [remainingTime, setRemainingTime] = useState(90);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [hasNarrated, setHasNarrated] = useState(false);

  // Handlers
  const stopRecognition = useCallback(async () => {
    if (mediaRecorderRef.current && isListening) {
      // Wait for any pending transcription to complete
      if (currentQuestion?.answer) {
        // Ensure current answer is saved
        updateAnswer(currentQuestion.answer);
      }
      
      // Stop recording after answer is saved
      mediaRecorderRef.current.stop();
      setIsListening(false);
      
      // Give time for final processing
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }, [isListening, currentQuestion, updateAnswer]);

  // Initialize refs
  const timerRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioChunks = useRef([]);
  const audioElement = useRef(null);
  const timerExpirationInProgress = useRef(false);

  // Handle audio transcription and answers
  const transcribeAudio = useCallback(
    async (audioBlob) => {
      if (!audioBlob || audioBlob.size === 0) {
        console.warn("No audio data to transcribe");
        console.log("Current answer state:", questions[currentIndex]?.answer);
        // Don't update answer, keep previous state
        return;
      }

      setIsProcessing(true);

      try {

        const file = new File([audioBlob], "audio.webm", {
          type: "audio/webm",
        });


        const formData = new FormData();
        formData.append("file", file);

        const response = await fetch("/api/v1/transcribe", {
          method: "POST",
          body: formData,
          credentials: "include",
        });
try {
  const responseData = await response.json();
  // console.log("Transcription response:", responseData);

  if (!response.ok) {
    throw new Error(
      `Transcription failed: ${JSON.stringify(responseData)}`
    );
  }

  if (!responseData.text) {
    throw new Error("No transcription text in response");
  }

await updateAnswer(responseData.text);

  const updated = [...questions];
updated[currentIndex] = {
  ...updated[currentIndex],
  answer: responseData.text,
  lastModified: new Date().toISOString(),
  endTimestamp: new Date().toISOString()
};
localStorage.setItem("questions", JSON.stringify(updated));
  toast.success("Answer recorded successfully");
} catch (parseError) {
  console.error("Response parsing error:", parseError);
  throw new Error(`Invalid response: ${parseError.message}`);
}
      } catch (error) {
        console.error("Transcription error:", error);

        let errorMessage =
          "Could not transcribe audio - please type your answer";

        if (error.message.includes("Unsupported audio format")) {
          errorMessage = "Audio format not supported - please try again";
        } else if (error.message.includes("No transcription text")) {
          errorMessage =
            "No speech detected - please speak clearly and try again";
        } else if (error.message.includes("Invalid response")) {
          errorMessage = "Server error - please try again";
        }

        toast.error(errorMessage, { duration: 4000 });
        // Don't update answer on error, keep previous answer
      } finally {
        setIsProcessing(false);
      }
    },
    [questions, currentIndex, updateAnswer]
  );

  // Effect to handle question narration
  useEffect(() => {
    console.log("=== NARRATION EFFECT TRIGGERED ===", {
      hasQuestion: !!currentQuestion?.question,
      hasNarrated,
      questionType: currentQuestion?.type,
      currentIndex,
      questionText: currentQuestion?.question?.substring(0, 50) + "...",
      isSpeaking,
      isProcessing,
      isInterviewActive
    });

    // Early returns for invalid states
    if (!isInterviewActive) {
      console.log("Skipping narration - interview not active yet");
      return;
    }

    if (!currentQuestion?.question) {
      console.log("Skipping narration - no question");
      return;
    }

    if (isProcessing) {
      console.log("Skipping narration - currently processing");
      return;
    }

    if (isSpeaking) {
      console.log("Skipping narration - already speaking");
      return;
    }

    // Check if this question has already been narrated
    if (hasNarrated) {
      console.log("Skipping narration - already narrated this question");
      return;
    }

    // Stop any existing audio
    if (audioElement.current) {
      audioElement.current.pause();
      audioElement.current.src = "";
    }

    let autoRecordTimeout;

    const speakQuestion = async () => {
      try {
        // Ensure we use the same question source as the display component
        // Use questions[currentIndex] to match what's displayed, with fallback to currentQuestion
        const questionFromArray = questions && questions[currentIndex] ? questions[currentIndex].question : null;
        const questionToSpeak = questionFromArray || currentQuestion?.question || "What is your experience with [skill]?";

        // Add logging for debugging synchronization issues
        console.log("TTS Debug - Question 3 sync check:", {
          currentIndex,
          questionFromArray,
          currentQuestionProp: currentQuestion?.question,
          finalQuestionToSpeak: questionToSpeak,
          questionsArrayLength: questions?.length
        });

        const params = {
          Text: questionToSpeak,
          OutputFormat: "mp3",
          VoiceId: "Raveena",
          SampleRate: "16000",
          TextType: "text",
        };

        const command = new SynthesizeSpeechCommand(params);
        const data = await pollyClient.send(command);

        if (data.AudioStream) {
          const audioBuffer = await new Response(
            data.AudioStream
          ).arrayBuffer();
          const audioBlob = new Blob([new Uint8Array(audioBuffer)], {
            type: "audio/mpeg",
          });

          const audio = new Audio(URL.createObjectURL(audioBlob));
          audioElement.current = audio;

          audio.onended = () => {
            setIsSpeaking(false);

                        // Auto-start recording for behavioral questions after narration
            if (currentQuestion?.type?.toLowerCase() === "behavioral") {
              // Small delay to ensure UI updates
              autoRecordTimeout = setTimeout(() => {
                handleSpeechRecognition().catch(err => {
                  console.error("Failed to auto-start recording:", err);
                  toast.error("Failed to auto-start recording. Please use the manual button.");
                });
              }, 500);
            }
          };
            
          setIsSpeaking(true);
          await audio.play();

          // setRemainingTime(currentQuestion.type === "verbal" ? 30 : 90);
          setRemainingTime(90);
          timerExpirationInProgress.current = false;
        }
      } catch (err) {
        console.error("Error during TTS:", err);
        setIsSpeaking(false);
      }
    };

    speakQuestion();
    setHasNarrated(true);

    return () => {
      if (audioElement.current) {
        audioElement.current.pause();
        audioElement.current.src = "";
      }
      if (autoRecordTimeout) {
        clearTimeout(autoRecordTimeout);
      }
      setIsSpeaking(false);
    };
  }, [currentQuestion?.question, currentIndex, hasNarrated, isSpeaking, isProcessing, isInterviewActive, questions]); // Include questions array for synchronization

    // Special effect to handle initial question load and ensure first question is narrated
  useEffect(() => {
    // For the first question, ensure narration happens
    if (currentQuestion?.question && currentIndex === 0 && hasNarrated) {
      setHasNarrated(false);
    }
  }, [currentQuestion?.question, currentIndex]);

  // Handler for moving to next question
  const handleNextQuestion = useCallback(async () => {
    if (isProcessing || isSpeaking) {
      return false;
    }

    try {
      setIsProcessing(true);
      
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      // Stop current audio playback
      if (audioElement.current) {
        audioElement.current.pause();
        audioElement.current.src = "";
      }
  
      // Ensure current answer is saved before proceeding
      const currentAnswer = currentQuestion?.answer;
      if (currentAnswer !== undefined) {
        await updateAnswer(currentAnswer);
        // Wait for state update
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // Stop recognition and wait for completion
      await stopRecognition();
      
      timerExpirationInProgress.current = false;
      await new Promise(resolve => setTimeout(resolve, 100));
      // Move to next question and reset narration flag
      await onNextQuestion();
      setHasNarrated(false);
      await new Promise(resolve => setTimeout(resolve, 300));
      return true;
    } catch (error) {
      console.error("Error moving to next question:", error);
      toast.error("Failed to move to next question. Please try again.");
      return false;
    } finally {
      setIsProcessing(false);
    }
  }, [questions, currentIndex, onNextQuestion, stopRecognition, updateAnswer, isSpeaking]);

  // Handler for ending the interview
  const handleEndInterview = useCallback(async () => {
    try {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      // Set timer expiration flag to prevent any timer interference
      timerExpirationInProgress.current = true;

      // Stop audio playback and recording
      if (audioElement.current) {
        audioElement.current.pause();
        audioElement.current.src = "";
      }
      setIsSpeaking(false);

      // Final verification of answers


      // Stop recording and wait for completion
      await stopRecognition();
      
      // Additional delay to ensure everything is saved
      await new Promise((resolve) => setTimeout(resolve, 1000));

      onEndInterview();
    } catch (error) {
      console.error("Error ending interview:", error);
      toast.error("Error saving answers. Please try again.");
    }finally {
      // Reset the timer expiration flag
      timerExpirationInProgress.current = false;
    }
  }, [stopRecognition, onEndInterview, questions, currentIndex]);

  // Timer effect for question countdown - only start/stop based on speaking state and interview activity
  useEffect(() => {
    if (isInterviewActive && !isSpeaking && !isProcessing && remainingTime > 0 && !timerRef.current && !timerExpirationInProgress.current) {
      const timer = setInterval(() => {
        setRemainingTime((prevTime) => {

          if (prevTime <= 1) {
            // handleNextQuestion();
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);

      timerRef.current = timer;
    } else if ((!isInterviewActive || isSpeaking || isProcessing || timerExpirationInProgress.current) && timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isSpeaking, isProcessing, remainingTime, isInterviewActive]);

    // Separate effect to handle timer expiration
  useEffect(() => {
    if (remainingTime === 0 && !isProcessing && !isSpeaking && !timerExpirationInProgress.current) {
      // Check if we have completed enough questions (5 or more) to allow finishing
      const hasCompletedMinimumQuestions = currentIndex + 1 >= 5;

      // Check if this is the last available question
      const isLastQuestion = currentIndex >= questions.length - 1;

      if (hasCompletedMinimumQuestions && isLastQuestion) {
        // Clear the timer but don't advance to next question
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
        // Show a message to the user that they can finish the interview
        toast("Time's up! You can now finish the interview or continue answering.", {
          icon: "⏰",
          duration: 5000
        });
        return;
      }

      timerExpirationInProgress.current = true;

      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      // Use setTimeout to avoid calling handleNextQuestion during render
      setTimeout(() => {
        handleNextQuestion().finally(() => {
          // Reset the flag after the transition is complete
          timerExpirationInProgress.current = false;
        });
      }, 100);
    }
  }, [remainingTime, isProcessing, isSpeaking, currentIndex, questions.length]);

  // Start recording audio
  const handleSpeechRecognition = useCallback(async () => {
    if (isListening) return;

    try {
      startAnswering();
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      // Use WebM format which is natively supported
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: "audio/webm",
        audioBitsPerSecond: 128000,
      });
      mediaRecorderRef.current = mediaRecorder;
      audioChunks.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunks.current.push(event.data);
      };
      mediaRecorder.onstop = async () => {
        if (audioChunks.current.length > 0) {
          const audioBlob = new Blob(audioChunks.current, {
            type: "audio/webm",
          });
          await transcribeAudio(audioBlob).catch((error) => {
            console.error("Transcription error:", error);
            toast.error("Failed to transcribe audio");
          });
        }
      };

      mediaRecorder.start(1000); // Collect data every second
      setIsListening(true);
    } catch (error) {
      console.error("Error starting recording:", error);
      toast.error("Failed to start recording");
      throw error; // Re-throw to allow error handling in the parent
    }
  }, [isListening, startAnswering, transcribeAudio]);

  // Define error and loading UI
  const errorUI = (
    <div className="flex h-screen items-center justify-center">
      <div className="max-w-md rounded-lg bg-red-50 p-6 text-center">
        <h2 className="mb-4 text-xl font-semibold text-red-700">
          Interview Setup Error
        </h2>
        <p className="text-red-600">{error}</p>
        <div className="mt-6 flex justify-center space-x-4">
          <button
            onClick={() => (window.location.href = "/candidate/dashboard")}
            className="rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
          >
            Return to Dashboard
          </button>
          <button
            onClick={() => window.location.reload()}
            className="rounded-md bg-gray-600 px-4 py-2 text-white hover:bg-gray-700"
          >
            Retry
          </button>
        </div>
      </div>
    </div>
  );

  const loadingUI = (
    <div className="flex h-screen items-center justify-center">
      <div className="max-w-md rounded-lg bg-blue-50 p-6 text-center">
        <div className="mb-4">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600"></div>
        </div>
        <h2 className="mb-4 text-xl font-semibold text-blue-700">
          Loading Interview
        </h2>
        <p className="text-blue-600">
          Please wait while we prepare your interview questions...
        </p>
      </div>
    </div>
  );


  // Handle special states with more detailed validation
  if (error) return errorUI;
  if (isLoading) return loadingUI;

  // Validate questions array
  if (!questions || !Array.isArray(questions) || questions.length === 0) {
    console.error("Invalid questions array:", questions);
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="max-w-md rounded-lg bg-red-50 p-6 text-center">
          <h2 className="mb-4 text-xl font-semibold text-red-700">
            Interview Setup Error
          </h2>
          <p className="text-red-600">No interview questions available</p>
          <div className="mt-6 flex justify-center space-x-4">
            <button
              onClick={() => window.location.reload()}
              className="rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Validate current question and index
  if (
    !currentQuestion ||
    typeof currentIndex !== "number" ||
    currentIndex < 0 ||
    currentIndex >= questions.length
  ) {
    console.error("Invalid question state:", {
      currentQuestion,
      currentIndex,
      questionsLength: questions.length,
    });
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="max-w-md rounded-lg bg-red-50 p-6 text-center">
          <h2 className="mb-4 text-xl font-semibold text-red-700">
            Question Loading Error
          </h2>
          <p className="text-red-600">Failed to load current question</p>
          <div className="mt-6 flex justify-center space-x-4">
            <button
              onClick={() => window.location.reload()}
              className="rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Render main UI
  return (
    <div className="mx-auto flex h-screen w-full max-w-full flex-col rounded-xl p-4">
      <div className="flex h-full w-full flex-row justify-between space-x-2">
        <div className="flex flex-grow flex-col justify-between space-y-2 lg:w-3/5">
          {localCamStream && <VideoPreview stream={localCamStream} />}
          <QuestionDisplay
            question={
              (questions && questions[currentIndex] ? questions[currentIndex].question : null) ||
              currentQuestion?.question ||
              "What is your experience with [skill]?"
            }
            followUp={
              (questions && questions[currentIndex] ? questions[currentIndex].follow_up : null) ||
              currentQuestion?.follow_up
            }
            isSpeaking={isSpeaking}
            isListening={isListening}
          />
        </div>
        <div className="flex flex-col items-center justify-center space-y-1 lg:w-2/5">
          <div className="flex h-12 w-full items-center justify-between rounded-xl p-1">
            {/* Question progress tracker */}
            <div className="flex space-x-2">
              {Array.from({ length: 5 }, (_, index) => (
                <div
                  key={index}
                  className={`flex h-9 w-9 items-center justify-center rounded-full ${
                    index <= currentIndex
                      ? "bg-blue-400 text-white"
                      : "bg-gray-300 text-gray-500"
                  }`}
                >
                  Q{index + 1}
                </div>
              ))}
            </div>

            {/* Indicators for Mic and Video */}
            <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
              <div className="relative flex items-center">
                <Mic />
                <span className="absolute right-0 top-0 h-2.5 w-2.5 animate-pulse rounded-full bg-green-500"></span>
              </div>
              <div className="relative flex items-center">
                <Video />
                <span className="absolute right-0 top-0 h-2.5 w-2.5 animate-pulse rounded-full bg-green-500"></span>
              </div>
            </div>
          </div>

          {currentQuestion && questions && questions[currentIndex] && (
            <AnswerInput
              question={currentQuestion}
              handleSpeechRecognition={handleSpeechRecognition}
              isListening={isListening}
              handleNextQuestion={handleNextQuestion}
              onEndInterview={async () => {
                if (
                  window.confirm("Are you sure you want to end the interview?")
                ) {
                  // Ensure final answer is saved before ending
                  const currentAnswer = currentQuestion?.answer;
                  if (currentAnswer !== undefined) {
                    await updateAnswer(currentAnswer);
                    await new Promise(resolve => setTimeout(resolve, 200));
                  }
                  handleEndInterview();
                }
              }}
              currentIndex={currentIndex}
              questions={questions}
              currentQuestion={questions[currentIndex]}
              isSpeaking={isSpeaking}
              remainingTime={remainingTime}
              isProcessing={isProcessing}
              startAnswering={startAnswering}
              updateAnswer={updateAnswer}
              isInterviewActive={isInterviewActive}
            />
          )}
        </div>
      </div>
    </div>
  );
}
